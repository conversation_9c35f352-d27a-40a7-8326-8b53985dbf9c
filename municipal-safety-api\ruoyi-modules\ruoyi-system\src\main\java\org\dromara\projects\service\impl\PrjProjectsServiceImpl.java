package org.dromara.projects.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.person.domain.bo.SysPersonBo;
import org.dromara.person.domain.vo.SysPersonVo;
import org.dromara.person.service.ISysPersonService;
import org.dromara.projects.domain.PrjProjects;
import org.dromara.projects.domain.bo.PrjProjectsBo;
import org.dromara.projects.domain.vo.PrjProjectsVo;
import org.dromara.projects.mapper.PrjProjectsMapper;
import org.dromara.projects.service.IPrjProjectsService;
import org.dromara.system.domain.SysEnterpriseInfo;
import org.dromara.system.domain.vo.EnterpriseNameAndId;
import org.dromara.system.service.ISysDeptService;
import org.dromara.system.service.ISysEnterpriseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 项目录入Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@RequiredArgsConstructor
@Service
public class PrjProjectsServiceImpl implements IPrjProjectsService {

    private final PrjProjectsMapper baseMapper;

    private final ISysDeptService deptService;

    private final ISysPersonService sysPersonService;

    private final ISysEnterpriseInfoService sysEnterpriseInfoService;

    /**
     * 查询项目录入
     *
     * @param projectId 主键
     * @return 项目录入
     */
    @Override
    public PrjProjectsVo queryById(Long projectId) {
        return baseMapper.selectVoById(projectId);
    }

    /**
     * 分页查询项目录入列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目录入分页列表
     */
    @Override
    public TableDataInfo<PrjProjectsVo> queryPageList(PrjProjectsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjProjects> lqw = buildQueryWrapper(bo);
        Page<PrjProjectsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 分页查询项目录入列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 项目录入分页列表
     */
    @Override
    public TableDataInfo<PrjProjectsVo> queryPageListSq(PrjProjectsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PrjProjects> lqw = buildQueryWrapper(bo);
        Page<PrjProjectsVo> result = baseMapper.selectVoPageSq(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<PrjProjectsVo> selectAll(PrjProjectsBo bo) {
        return baseMapper.selectAll(buildQueryWrapper(bo));
    }

    @Override
    public List<PrjProjectsVo> listAiAll(PrjProjectsBo bo) {
        return baseMapper.selectVoListSq(buildQueryWrapper(bo));
    }

    /**
     * 查询符合条件的项目录入列表
     *
     * @param bo 查询条件
     * @return 项目录入列表
     */
    @Override
    public List<PrjProjectsVo> queryList(PrjProjectsBo bo) {
        LambdaQueryWrapper<PrjProjects> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrjProjects> buildQueryWrapper(PrjProjectsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrjProjects> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PrjProjects::getProjectId);
        lqw.like(StringUtils.isNotBlank(bo.getProjectName()), PrjProjects::getProjectName, bo.getProjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectCode()), PrjProjects::getProjectCode, bo.getProjectCode());
        lqw.eq(StringUtils.isNotBlank(bo.getConstructionPermitNo()), PrjProjects::getConstructionPermitNo, bo.getConstructionPermitNo());
        lqw.eq(StringUtils.isNotBlank(bo.getProvinceCode()), PrjProjects::getProvinceCode, bo.getProvinceCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCityCode()), PrjProjects::getCityCode, bo.getCityCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDistrictCode()), PrjProjects::getDistrictCode, bo.getDistrictCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PrjProjects::getStatus, bo.getStatus());
        // 仅查询未删除
        lqw.eq(PrjProjects::getDelFlag, 0);
        // 如果传入了项目ID集合，按集合过滤（支持“项目管理员仅看自己项目”）
        if (bo.getProjectIdList() != null && !bo.getProjectIdList().isEmpty()) {
            java.util.List<Long> ids = bo.getProjectIdList().stream()
                .filter(org.dromara.common.core.utils.StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(java.util.stream.Collectors.toList());
            if (!ids.isEmpty()) {
                lqw.in(PrjProjects::getProjectId, ids);
            }
        }
        return lqw;
    }

    /**
     * 新增项目录入
     *
     * @param bo 项目录入
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PrjProjectsBo bo) {
        PrjProjects add = MapstructUtils.convert(bo, PrjProjects.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProjectId(add.getProjectId());
        }
        return flag;
    }

    /**
     * 修改项目录入
     *
     * @param bo 项目录入
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PrjProjectsBo bo) {
        PrjProjects update = MapstructUtils.convert(bo, PrjProjects.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrjProjects entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除项目录入信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SysPersonVo> selectFivePartyUsers(SysPersonBo bo, PageQuery pageQuery) {
        if (bo.getDeptId() != null) {
            Long deptId = bo.getDeptId();
            LambdaQueryWrapper<SysEnterpriseInfo> lqw_enterprise = Wrappers.lambdaQuery();
            lqw_enterprise.eq(SysEnterpriseInfo::getDeptId, deptId);
            List<SysEnterpriseInfo> enterpriseInfoList = sysEnterpriseInfoService.list(lqw_enterprise);
            if (CollUtil.isEmpty(enterpriseInfoList)) {
                return TableDataInfo.build(new Page<>());
            }
            SysEnterpriseInfo sysEnterpriseInfo = CollUtil.getFirst(enterpriseInfoList);
            bo.setEnterpriseId(sysEnterpriseInfo.getEnterpriseId());
        }
        // 查询某个单位下的所有人员
        return sysPersonService.queryPageList(bo, pageQuery);
    }

    @Override
    public R<List<EnterpriseNameAndId>> getSearchData() {

        List<EnterpriseNameAndId> list = new ArrayList<>();

        LambdaQueryWrapper<PrjProjects> wrapper = Wrappers.lambdaQuery();
        wrapper.select(PrjProjects::getProjectId, PrjProjects::getProjectName);

        List<PrjProjects> prjProjects = this.baseMapper.selectList(wrapper);

        if (!prjProjects.isEmpty()) {
            for (PrjProjects project : prjProjects) {
                list.add(new EnterpriseNameAndId(project.getProjectId(), project.getProjectName()));
            }
        }

        return R.ok(list);
    }
}
