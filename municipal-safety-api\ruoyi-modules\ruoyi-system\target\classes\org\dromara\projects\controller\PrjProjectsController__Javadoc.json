{"doc": " 项目录入\n\n <AUTHOR>\n @date 2025-05-07\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目录入列表\n"}, {"name": "listAi", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询项目录入列表\n"}, {"name": "listAiAll", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo"], "doc": " 查询项目录入列表\n"}, {"name": "export", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出项目录入列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取项目录入详细信息\n\n @param projectId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo"], "doc": " 新增项目录入\n"}, {"name": "edit", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo"], "doc": " 修改项目录入\n"}, {"name": "bindPersonnel", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 绑定项目人员（支持单独绑定，不必通过编辑项目）\n"}, {"name": "bindAdminQuick", "paramTypes": ["java.lang.Long", "org.dromara.projects.controller.PrjProjectsController.AdminBindRequest"], "doc": " 快捷绑定项目管理员（政府侧使用，支持新建人员）\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除项目录入\n\n @param projectIds 主键串\n"}, {"name": "selectAll", "paramTypes": ["org.dromara.projects.domain.bo.PrjProjectsBo"], "doc": " 查询企业下的项目列表\n"}, {"name": "selectFivePartyUsers", "paramTypes": ["org.dromara.person.domain.bo.SysPersonBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询五方单位下任意一方的人员列表\n"}, {"name": "bindProjectPersonnel", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": " 绑定项目人员关联\n\n @param projectId 项目ID\n @param personIds 人员ID列表（格式：personId,orgId,roleOnProject|personId,orgId,roleOnProject）\n"}, {"name": "getSearchData", "paramTypes": [], "doc": " 查询所有项目的名称和id\n\n @return\n"}, {"name": "syncProject", "paramTypes": ["java.lang.String"], "doc": " 根据施工许可证编号同步项目信息\n\n @param constructionPermitNo 施工许可证编号\n"}, {"name": "syncProject", "paramTypes": [], "doc": " 批量从中间表 xmxx 根据施工许可证编号同步项目信息\n"}, {"name": "getSupervise", "paramTypes": [], "doc": " 获取监管端大屏地址\n\n @return\n"}, {"name": "getProjectUrl", "paramTypes": [], "doc": " 获取监管端大屏地址\n\n @return\n"}], "constructors": []}