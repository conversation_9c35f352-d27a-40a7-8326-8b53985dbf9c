import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { Prj_projectsVO, Prj_projectsForm, Prj_projectsQuery } from '@/api/projects/prj_projects/types';
import { DeptQuery } from '@/api/system/dept/types';

/**
 * 查询项目录入列表
 * @param query
 * @returns {*}
 */

export const listPrj_projects = (query?: Prj_projectsQuery): AxiosPromise<Prj_projectsVO[]> => {
  return request({
    url: '/projects/prj_projects/list',
    method: 'get',
    params: query
  });
};

/**
 * 根据施工许可证编号同步项目信息
 * @param constructionPermitNo
 */
export const sync_project = (constructionPermitNo) => {
  return request({
    url: '/projects/prj_projects/syncProject/' + constructionPermitNo,
    method: 'get'
  });
};

/**
 * 查询项目录入详细
 * @param projectId
 */
export const getPrj_projects = (projectId: string | number): AxiosPromise<Prj_projectsVO> => {
  return request({
    url: '/projects/prj_projects/' + projectId,
    method: 'get'
  });
};


/**
 * 新增项目录入
 * @param data
 */
export const addPrj_projects = (data: Prj_projectsForm) => {
  return request({
    url: '/projects/prj_projects',
    method: 'post',
    data: data
  });
};

/**
 * 修改项目录入
 * @param data
 */
export const updatePrj_projects = (data: Prj_projectsForm) => {
  return request({
    url: '/projects/prj_projects',
    method: 'put',
    data: data
  });
};

/**
 * 删除项目录入
 * @param projectId
 */
export const delPrj_projects = (projectId: string | number | Array<string | number>) => {
  return request({
    url: '/projects/prj_projects/' + projectId,
    method: 'delete'
  });
};

// 查询五方单位下任意一方的人员列表
export const getEnterpriseUserList = (params: any) => {
  return request({
    url: '/projects/prj_projects/selectFivePartyUsers',
    method: 'get',
    params
  });
};

// 删除人员

export const delpersonnel = (projectPersonnelIds: any) => {
  return request({
    url: '/projects/personnel/' + projectPersonnelIds,
    method: 'DELETE',
  });
};


// 查询部门列表 忽略权限
export const listDept = (query?: DeptQuery) => {
  return request({
    url: '/projects/prj_projects/dept/list',
    method: 'get',
    params: query
  });
};

// 考勤(日)

export const getPersonAtt = (personId, attDate) => {
  return request({
    url: '/attendance/attRecord/getPersonAtt/' + personId + '/' + attDate,
    method: 'get',
  });
};

// 考勤(月)
export const getPersonAttMonth = (personId, attDate) => {
  return request({
    url: '/attendance/attRecord/getPersonAttByMonth/' + personId + '/' + attDate,
    method: 'get',
  });
};

export const getProUser = (projectId) => {
  return request({
    url: `/projects/personnel/listByProjectId/${projectId}`,
    method: 'get'
  })
}

export const postUserIssued = (data) => {
  return request({
    url: '/projects/personnel/batch/distribute',
    method: 'post',
    data
  });
};

export const getFaceMachine = (projectId, personId) => {
  return request({
    url: `/attendance/attSn/selectMAttSn/${projectId}/${personId}`,
    method: 'get'
  })
}

export const removeUserIssued = (data) => {
  return request({
    url: `/projects/personnel/removeSns`,
    method: 'post',
    data
  })
}

// 监管端大屏
export const getSupervisionScreen = () => {
  return request({
    url: '/projects/prj_projects/getSuperviseUrl',
    method: 'get'
  })
}
// 项目大屏
export const getProjectScreen = () => {
  return request({
    url: '/projects/prj_projects/getProjectUrl',
    method: 'get'
  })
}

// 单独绑定项目人员
export const bindProjectPersonnel = (projectId: number | string, personIds: string) => {
  return request({
    url: `/projects/prj_projects/${projectId}/bindPersonnel`,
    method: 'post',
    data: personIds,
    headers: { 'Content-Type': 'text/plain;charset=UTF-8' }
  });
};

// 快捷绑定项目管理员
export const bindAdminQuick = (projectId: number | string, data: {
  name: string;
  idCard: string;
  phone: string;
  orgId?: number;
}) => {
  return request({
    url: `/projects/prj_projects/${projectId}/bindAdminQuick`,
    method: 'post',
    data
  });
};


